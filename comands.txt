# Create a virtual environment named 'venv'
python3 -m venv venv

# On Linux or macOS, use the following command to activate the virtual environment.
# For Windows, use: venv\Scripts\activate
source venv/bin/activate

# Install dependencies from the 'requirements.txt' file
pip3 install -r requirements.txt

# Install pre-commit package
sudo apt install pre-commit

# This will run all the pre-commit hooks for the files in your project, checking for any issues based on the defined hooks.
pre-commit run --all-files

# This installs pipreqs, a tool that generates a requirements.txt file based on imports in your code.
pip3 install pipreqs

# Regenerate the 'requirements.txt' file
pipreqs . --force
