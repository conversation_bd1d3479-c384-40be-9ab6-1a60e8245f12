<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Stock Analysis - Egyptian Exchange</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            margin: 20px 0;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .data-container {
            margin-top: 20px;
        }
        .pair-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .pair-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        .pair-content {
            padding: 20px;
        }
        .price-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        .price-value {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .interval-section {
            margin-bottom: 25px;
        }
        .interval-header {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
        }
        .indicators-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .indicator-group {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            overflow: hidden;
        }
        .indicator-group h4 {
            background: #f8f9fa;
            margin: 0;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }
        .indicator-table {
            width: 100%;
            border-collapse: collapse;
        }
        .indicator-table th,
        .indicator-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #f1f3f4;
        }
        .indicator-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 12px;
        }
        .indicator-table td {
            font-size: 13px;
        }
        .action-buy {
            color: #28a745;
            font-weight: bold;
        }
        .action-sell {
            color: #dc3545;
            font-weight: bold;
        }
        .action-neutral {
            color: #6c757d;
            font-weight: bold;
        }
        .pivots-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .pivots-table th,
        .pivots-table td {
            padding: 8px 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .pivots-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        @media (max-width: 768px) {
            .indicators-grid {
                grid-template-columns: 1fr;
            }
        }
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
        }
        .checkbox-item input {
            width: auto;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇪🇬 EGX Stock Analysis</h1>
        <p>Advanced technical analysis for Egyptian Exchange stocks using real-time TradingView data.</p>

        <form id="scraperForm">
            <div class="form-group">
                <label for="egx-pairs">📈 Egyptian Exchange (EGX) Stocks:</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-COMI" name="pairs" value="EGX-COMI" checked>
                        <label for="EGX-COMI">COMI (Commercial International Bank)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-FWRY" name="pairs" value="EGX-FWRY">
                        <label for="EGX-FWRY">FWRY (Fawry Banking Technology)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-PHDC" name="pairs" value="EGX-PHDC">
                        <label for="EGX-PHDC">PHDC (Palm Hills Development)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-EFID" name="pairs" value="EGX-EFID">
                        <label for="EGX-EFID">EFID (Edita Food Industries)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-UBEE" name="pairs" value="EGX-UBEE">
                        <label for="EGX-UBEE">UBEE (United Bank Egypt)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-GGRN" name="pairs" value="EGX-GGRN">
                        <label for="EGX-GGRN">GGRN (GoGreen Agricultural)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-OBRI" name="pairs" value="EGX-OBRI">
                        <label for="EGX-OBRI">OBRI (Orascom Business Intelligence)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="EGX-UTOP" name="pairs" value="EGX-UTOP">
                        <label for="EGX-UTOP">UTOP (United Top)</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="intervals">Time Intervals:</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="1h" name="intervals" value="1h">
                        <label for="1h">1 Hour</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="4h" name="intervals" value="4h">
                        <label for="4h">4 Hours</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="1d" name="intervals" value="1d" checked>
                        <label for="1d">1 Day</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="1w" name="intervals" value="1w">
                        <label for="1w">1 Week</label>
                    </div>
                </div>
            </div>

            <button type="submit" id="submitBtn">📊 Analyze EGX Stocks</button>
        </form>

        <div id="loading" class="loading" style="display: none;">
            ⏳ Analyzing EGX stocks from TradingView... Please wait...
        </div>

        <div id="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('scraperForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get selected pairs
            const selectedPairs = Array.from(document.querySelectorAll('input[name="pairs"]:checked'))
                .map(cb => cb.value);
            
            // Get selected intervals
            const selectedIntervals = Array.from(document.querySelectorAll('input[name="intervals"]:checked'))
                .map(cb => cb.value);
            
            if (selectedPairs.length === 0) {
                showResult('Please select at least one EGX stock.', 'error');
                return;
            }

            if (selectedIntervals.length === 0) {
                showResult('Please select at least one time interval.', 'error');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            document.getElementById('submitBtn').disabled = true;

            try {
                const response = await fetch('/api/scrape_pairs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pairs: selectedPairs,
                        intervals: selectedIntervals
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showFormattedResult(data.result, selectedPairs, selectedIntervals);
                } else {
                    showResult(`❌ Error: ${data.message || 'Unknown error occurred'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('submitBtn').disabled = false;
            }
        });

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<pre>${message}</pre>`;
            resultDiv.style.display = 'block';
        }

        function showFormattedResult(data, selectedPairs, selectedIntervals) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result success';

            let html = `<div class="data-container">
                <h3>✅ Success! Analyzed EGX stocks: ${selectedPairs.join(', ')} at ${selectedIntervals.join(', ')} intervals.</h3>`;

            // Process each trading pair
            Object.keys(data).forEach(pairName => {
                const pairData = data[pairName];

                html += `<div class="pair-section">
                    <div class="pair-header">${pairName}</div>
                    <div class="pair-content">`;

                // Group data by interval
                const intervalGroups = {};
                pairData.forEach(item => {
                    if (item.oscillators && item.oscillators.length > 0) {
                        const interval = item.oscillators[0].interval;
                        if (!intervalGroups[interval]) {
                            intervalGroups[interval] = item;
                        }
                    }
                });

                // Display price (same for all intervals)
                if (pairData.length > 0) {
                    html += `<div class="price-info">
                        <div>Current Price</div>
                        <div class="price-value">${pairData[0].price.toLocaleString()} EGP</div>
                    </div>`;
                }

                // Display data for each interval
                Object.keys(intervalGroups).forEach(interval => {
                    const intervalData = intervalGroups[interval];

                    html += `<div class="interval-section">
                        <div class="interval-header">📊 ${interval.toUpperCase()} Analysis</div>

                        <div class="indicators-grid">
                            <div class="indicator-group">
                                <h4>🔄 Oscillators</h4>
                                <table class="indicator-table">
                                    <thead>
                                        <tr>
                                            <th>Indicator</th>
                                            <th>Value</th>
                                            <th>Signal</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                    intervalData.oscillators.forEach(osc => {
                        const actionClass = `action-${osc.action.toLowerCase()}`;
                        html += `<tr>
                            <td>${osc.name}</td>
                            <td>${osc.value}</td>
                            <td class="${actionClass}">${osc.action}</td>
                        </tr>`;
                    });

                    html += `</tbody></table></div>

                            <div class="indicator-group">
                                <h4>📈 Moving Averages</h4>
                                <table class="indicator-table">
                                    <thead>
                                        <tr>
                                            <th>Indicator</th>
                                            <th>Value</th>
                                            <th>Signal</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                    intervalData.moving_averages.forEach(ma => {
                        const actionClass = `action-${ma.action.toLowerCase()}`;
                        html += `<tr>
                            <td>${ma.name}</td>
                            <td>${ma.value}</td>
                            <td class="${actionClass}">${ma.action}</td>
                        </tr>`;
                    });

                    html += `</tbody></table></div>
                        </div>

                        <div class="indicator-group">
                            <h4>🎯 Pivot Points</h4>
                            <table class="pivots-table">
                                <thead>
                                    <tr>
                                        <th>Level</th>
                                        <th>Classic</th>
                                        <th>Fibonacci</th>
                                        <th>Camarilla</th>
                                        <th>Woodie</th>
                                        <th>DM</th>
                                    </tr>
                                </thead>
                                <tbody>`;

                    intervalData.pivots.forEach(pivot => {
                        html += `<tr>
                            <td><strong>${pivot.pivot}</strong></td>
                            <td>${pivot.classic}</td>
                            <td>${pivot.fibo}</td>
                            <td>${pivot.camarilla}</td>
                            <td>${pivot.woodie}</td>
                            <td>${pivot.dm || '-'}</td>
                        </tr>`;
                    });

                    html += `</tbody></table>
                        </div>
                    </div>`;
                });

                html += `</div></div>`;
            });

            html += '</div>';

            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
