import requests
import json

# Test the TradingView scraper API
url = "http://127.0.0.1:8000/api/scrape_pairs"

# Test data - multiple pairs and intervals
data = {
    "pairs": ["BTCUSD", "ETHUSD"],
    "intervals": ["1h", "4h"]
}

print("Testing TradingView Scraper API...")
print(f"URL: {url}")
print(f"Data: {json.dumps(data, indent=2)}")

try:
    response = requests.post(url, json=data)
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API call successful!")
        print(f"Response: {json.dumps(result, indent=2)}")
    else:
        print("❌ API call failed!")
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"❌ Error making request: {e}")
