"""URL configuration for apidemo project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from ninja import NinjaAPI
from .scrape_pairs import main
from .models import PairRequest

api = NinjaAPI()


@api.post("/scrape_pairs")
async def scrape_pairs(request, pair_request: PairRequest):
    print(f"Received pair_request: {pair_request}")
    res = await main(pair_request)
    return {"result": res}


urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", api.urls),
]
