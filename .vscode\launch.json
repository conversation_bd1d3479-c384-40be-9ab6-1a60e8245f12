{"version": "0.2.0", "configurations": [{"name": "Django Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/src/apidemo/manage.py", "args": ["runserver"], "django": true, "justMyCode": true, "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}/src/apidemo"}, {"name": "Test API", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_api.py", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}"}]}