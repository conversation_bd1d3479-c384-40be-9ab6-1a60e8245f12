# Integration Examples for AI Stock Bot

## Option 1: Direct Code Integration
"""
Replace your existing scraper with these core functions:
1. Copy scrape_pairs.py and models.py to your project
2. Install dependencies: playwright, pydantic
3. Use the main() function in your AI bot
"""

import asyncio
import requests
from typing import Dict, List, Any

# Option 2: API Integration - Use current app as microservice
class TradingViewAPI:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
    
    async def get_stock_data(self, pairs: List[str], intervals: List[str]) -> Dict[str, Any]:
        """
        Fetch stock data from the TradingView scraper API
        
        Args:
            pairs: List of stock symbols (e.g., ['EGX-COMI', 'EGX-FWRY'])
            intervals: List of timeframes (e.g., ['1h', '1d'])
        
        Returns:
            Dictionary with comprehensive technical analysis data
        """
        url = f"{self.base_url}/api/scrape_pairs"
        payload = {
            "pairs": pairs,
            "intervals": intervals
        }
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API Error: {e}")
            return None

# Option 3: Hybrid Integration - Extract specific functions
class AIStockBot:
    def __init__(self):
        self.api = TradingViewAPI()
    
    async def get_enhanced_data_for_prediction(self, stock_symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive data for AI prediction model
        
        Returns structured data with:
        - Current price
        - Technical indicators (RSI, MACD, etc.)
        - Moving averages
        - Pivot points
        - Trading signals
        """
        # Get data for multiple timeframes for better AI prediction
        intervals = ['1h', '4h', '1d']  # Short, medium, long term
        
        result = await self.api.get_stock_data([stock_symbol], intervals)
        
        if not result or 'result' not in result:
            return None
            
        stock_data = result['result'].get(stock_symbol, [])
        
        # Structure data for AI model
        enhanced_data = {
            'symbol': stock_symbol,
            'current_price': stock_data[0]['price'] if stock_data else None,
            'timeframes': {}
        }
        
        for data in stock_data:
            interval = data['oscillators'][0]['interval'] if data['oscillators'] else 'unknown'
            
            enhanced_data['timeframes'][interval] = {
                'price': data['price'],
                'technical_indicators': {
                    'oscillators': {osc['name']: {'value': osc['value'], 'signal': osc['action']} 
                                  for osc in data['oscillators']},
                    'moving_averages': {ma['name']: {'value': ma['value'], 'signal': ma['action']} 
                                      for ma in data['moving_averages']},
                    'pivots': {pivot['pivot']: {'classic': pivot['classic'], 'fibonacci': pivot['fibo']} 
                             for pivot in data['pivots']}
                },
                'signals': {
                    'buy_signals': len([osc for osc in data['oscillators'] if osc['action'] == 'Buy']),
                    'sell_signals': len([osc for osc in data['oscillators'] if osc['action'] == 'Sell']),
                    'neutral_signals': len([osc for osc in data['oscillators'] if osc['action'] == 'Neutral'])
                }
            }
        
        return enhanced_data

# Example usage in your AI bot
async def main_ai_bot_example():
    bot = AIStockBot()
    
    # Get enhanced data for EGX stocks
    egx_stocks = ['EGX-COMI', 'EGX-FWRY', 'EGX-PHDC']
    
    for stock in egx_stocks:
        print(f"\n=== Analyzing {stock} ===")
        
        data = await bot.get_enhanced_data_for_prediction(stock)
        
        if data:
            print(f"Current Price: {data['current_price']}")
            
            # Example: Use 1h data for short-term prediction
            if '1h' in data['timeframes']:
                hourly_data = data['timeframes']['1h']
                
                # Extract features for AI model
                rsi = hourly_data['technical_indicators']['oscillators'].get('Relative Strength Index (14)', {}).get('value')
                macd = hourly_data['technical_indicators']['oscillators'].get('MACD Level (12, 26)', {}).get('value')
                ema_10 = hourly_data['technical_indicators']['moving_averages'].get('Exponential Moving Average (10)', {}).get('value')
                
                print(f"RSI: {rsi}")
                print(f"MACD: {macd}")
                print(f"EMA(10): {ema_10}")
                
                # Your AI prediction logic here
                # prediction = your_ai_model.predict([rsi, macd, ema_10, ...])
                
                # Trading signals summary
                signals = hourly_data['signals']
                print(f"Buy signals: {signals['buy_signals']}")
                print(f"Sell signals: {signals['sell_signals']}")
                print(f"Neutral signals: {signals['neutral_signals']}")

# Option 4: Standalone Scraper Module
class StandaloneScraper:
    """
    Extract just the scraping functionality for your existing bot
    """
    
    @staticmethod
    async def scrape_egx_stock(symbol: str, intervals: List[str] = ['1d']) -> Dict[str, Any]:
        """
        Simplified scraper function that you can integrate directly
        """
        from playwright.async_api import async_playwright
        import re
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                url = f"https://tradingview.com/symbols/{symbol}/technicals/"
                await page.goto(url, wait_until="domcontentloaded")
                
                # Get current price
                price_selector = ".lastContainer-zoF9r75I"
                await page.wait_for_selector(price_selector)
                price_element = page.locator(price_selector).first
                price_text = await price_element.text_content()
                current_price = float(re.sub(r"\D", "", price_text)) if price_text else None
                
                # Get basic technical indicators
                # You can expand this based on your needs
                
                return {
                    'symbol': symbol,
                    'price': current_price,
                    'timestamp': datetime.now().isoformat(),
                    # Add more indicators as needed
                }
                
            finally:
                await browser.close()

if __name__ == "__main__":
    # Test the integration
    asyncio.run(main_ai_bot_example())
