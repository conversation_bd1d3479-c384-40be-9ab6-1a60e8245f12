#!/usr/bin/env python3
"""
EGX Stock Analysis API Test Script
Test the EGX-focused TradingView scraper API
"""

import requests
import json
from datetime import datetime

# EGX API Configuration
API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

# Popular EGX stocks for testing
EGX_STOCKS = [
    "EGX-COMI",  # Commercial International Bank
    "EGX-FWRY",  # Fawry Banking Technology
    "EGX-PHDC",  # Palm Hills Development
    "EGX-EFID",  # Edita Food Industries
]

# Time intervals for analysis
INTERVALS = ["1D", "1W"]  # Daily and weekly analysis

def test_egx_api():
    """Test the EGX stock analysis API"""
    print("🇪🇬 Testing EGX Stock Analysis API")
    print("=" * 50)
    
    # Test data
    test_data = {
        "pairs": EGX_STOCKS[:2],  # Test with first 2 stocks
        "intervals": INTERVALS
    }
    
    print(f"📊 Testing stocks: {', '.join(test_data['pairs'])}")
    print(f"⏰ Time intervals: {', '.join(test_data['intervals'])}")
    print(f"🌐 API URL: {API_URL}")
    print("\n⏳ Sending request...")
    
    try:
        # Send API request
        response = requests.post(
            API_URL, 
            json=test_data,
            timeout=120  # 2 minutes timeout for stock analysis
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            
            # Display results
            display_egx_results(result.get('result', {}))
            
        else:
            print("❌ API call failed!")
            print(f"Error: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - EGX market might be closed or slow response")
    except requests.exceptions.RequestException as e:
        print(f"🔌 Network Error: {e}")
    except Exception as e:
        print(f"💥 Unexpected Error: {e}")

def display_egx_results(data):
    """Display EGX analysis results in a readable format"""
    if not data:
        print("📭 No data received")
        return
    
    print("\n" + "=" * 60)
    print("📈 EGX STOCK ANALYSIS RESULTS")
    print("=" * 60)
    
    for stock_symbol, stock_data in data.items():
        print(f"\n🏢 {stock_symbol}")
        print("-" * 40)
        
        if not stock_data:
            print("❌ No data available for this stock")
            continue
            
        # Display price
        current_price = stock_data[0].get('price', 'N/A')
        print(f"💰 Current Price: {current_price} EGP")
        
        # Process each timeframe
        for timeframe_data in stock_data:
            interval = "Unknown"
            if timeframe_data.get('oscillators'):
                interval = timeframe_data['oscillators'][0].get('interval', 'Unknown')
            
            print(f"\n📊 {interval.upper()} Analysis:")
            
            # Technical indicators summary
            oscillators = timeframe_data.get('oscillators', [])
            moving_averages = timeframe_data.get('moving_averages', [])
            
            if oscillators:
                # Count signals
                buy_signals = sum(1 for osc in oscillators if osc.get('action') == 'Buy')
                sell_signals = sum(1 for osc in oscillators if osc.get('action') == 'Sell')
                neutral_signals = sum(1 for osc in oscillators if osc.get('action') == 'Neutral')
                
                print(f"   🔄 Oscillators: {buy_signals} Buy, {sell_signals} Sell, {neutral_signals} Neutral")
                
                # Show key indicators
                for osc in oscillators[:3]:  # Show first 3 indicators
                    name = osc.get('name', 'Unknown')
                    value = osc.get('value', 'N/A')
                    action = osc.get('action', 'N/A')
                    print(f"      • {name}: {value} ({action})")
            
            if moving_averages:
                # Count MA signals
                ma_buy = sum(1 for ma in moving_averages if ma.get('action') == 'Buy')
                ma_sell = sum(1 for ma in moving_averages if ma.get('action') == 'Sell')
                ma_neutral = sum(1 for ma in moving_averages if ma.get('action') == 'Neutral')
                
                print(f"   📈 Moving Averages: {ma_buy} Buy, {ma_sell} Sell, {ma_neutral} Neutral")
            
            # Overall signal
            total_buy = buy_signals + ma_buy if 'buy_signals' in locals() and 'ma_buy' in locals() else 0
            total_sell = sell_signals + ma_sell if 'sell_signals' in locals() and 'ma_sell' in locals() else 0
            
            if total_buy > total_sell:
                signal = "🟢 BULLISH"
            elif total_sell > total_buy:
                signal = "🔴 BEARISH"
            else:
                signal = "⚪ NEUTRAL"
            
            print(f"   📊 Overall Signal: {signal}")

def test_single_stock(stock_symbol):
    """Test analysis for a single EGX stock"""
    print(f"\n🔍 Testing single stock: {stock_symbol}")
    
    test_data = {
        "pairs": [stock_symbol],
        "intervals": ["1D"]
    }
    
    try:
        response = requests.post(API_URL, json=test_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            display_egx_results(result.get('result', {}))
        else:
            print(f"❌ Failed to analyze {stock_symbol}: {response.text}")
            
    except Exception as e:
        print(f"💥 Error analyzing {stock_symbol}: {e}")

if __name__ == "__main__":
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test main API
    test_egx_api()
    
    # Test individual stocks
    print("\n" + "=" * 60)
    print("🔬 INDIVIDUAL STOCK TESTS")
    print("=" * 60)
    
    for stock in EGX_STOCKS[:2]:  # Test first 2 stocks individually
        test_single_stock(stock)
    
    print(f"\n🏁 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n💡 Note: If you see '—' values, the EGX market might be closed or data unavailable.")
    print("🕐 EGX Trading Hours: Sunday-Thursday, 10:00 AM - 2:30 PM (Cairo Time)")
